import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import LoginScreen from './src/screens/LoginScreen';
import MyCorrespondencesScreen from './src/screens/MyCorrespondencesScreen';
import PlannerScreen from './src/screens/PlannerScreen';

export type RootStackParamList = {
  Login: undefined;
  MyCorrespondences: undefined;
  Planner: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Login">
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="MyCorrespondences" component={MyCorrespondencesScreen} />
        <Stack.Screen name="Planner" component={PlannerScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
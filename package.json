{"name": "CorrespondexApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "react": "19.0.0", "react-native": "0.79.3", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.3", "@react-native/eslint-config": "0.79.3", "@react-native/metro-config": "0.79.3", "@react-native/typescript-config": "0.79.3", "@types/jest": "^29.5.13", "@types/react": "^19.1.8", "@types/react-native": "^0.72.8", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "sharp": "^0.34.2", "typescript": "^5.0.4"}, "engines": {"node": ">=18"}}
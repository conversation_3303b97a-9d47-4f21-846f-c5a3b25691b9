import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated, Dimensions } from 'react-native';
import Logo from '../components/Logo';

interface SplashScreenProps {
  onFinish: () => void;
}

export default function SplashScreen({ onFinish }: SplashScreenProps) {
  const [progress] = useState(new Animated.Value(0));
  const [logoScale] = useState(new Animated.Value(0.5));
  const [logoOpacity] = useState(new Animated.Value(0));
  const [textOpacity] = useState(new Animated.Value(0));
  const [progressOpacity] = useState(new Animated.Value(0));
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingText, setLoadingText] = useState('Initializing...');

  const screenWidth = Dimensions.get('window').width;
  const progressBarWidth = screenWidth * 0.7;

  useEffect(() => {
    // Animación de entrada del logo
    Animated.sequence([
      Animated.parallel([
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Mostrar barra de progreso después del texto
    setTimeout(() => {
      Animated.timing(progressOpacity, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }).start();
    }, 1000);

    // Simulación de progreso de carga con textos dinámicos
    const progressInterval = setInterval(() => {
      setLoadingProgress(prev => {
        const newProgress = prev + Math.random() * 12 + 3; // Incremento aleatorio entre 3-15

        // Cambiar texto según el progreso
        if (newProgress < 25) {
          setLoadingText('Initializing...');
        } else if (newProgress < 50) {
          setLoadingText('Loading components...');
        } else if (newProgress < 75) {
          setLoadingText('Setting up navigation...');
        } else if (newProgress < 95) {
          setLoadingText('Almost ready...');
        } else {
          setLoadingText('Welcome!');
        }

        if (newProgress >= 100) {
          clearInterval(progressInterval);
          setLoadingText('Ready!');
          // Esperar un poco antes de terminar
          setTimeout(() => {
            onFinish();
          }, 800);
          return 100;
        }
        return newProgress;
      });
    }, 180);

    return () => {
      clearInterval(progressInterval);
    };
  }, [logoOpacity, logoScale, textOpacity, progressOpacity, onFinish]);

  useEffect(() => {
    // Animar la barra de progreso
    Animated.timing(progress, {
      toValue: loadingProgress / 100,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [loadingProgress, progress]);

  return (
    <View style={styles.container}>
      {/* Logo animado */}
      <Animated.View 
        style={[
          styles.logoContainer,
          {
            opacity: logoOpacity,
            transform: [{ scale: logoScale }]
          }
        ]}
      >
        <Logo size={140} animated={true} />
      </Animated.View>

      {/* Nombre de la app */}
      <Animated.View style={[styles.textContainer, { opacity: textOpacity }]}>
        <Text style={styles.appName}>CorrespondexApp</Text>
        <Text style={styles.subtitle}>Manage your correspondence efficiently</Text>
      </Animated.View>

      {/* Barra de progreso */}
      <Animated.View style={[styles.progressContainer, { opacity: progressOpacity }]}>
        <View style={[styles.progressBar, { width: progressBarWidth }]}>
          <Animated.View
            style={[
              styles.progressFill,
              {
                width: progress.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, progressBarWidth],
                  extrapolate: 'clamp',
                })
              }
            ]}
          />
          {/* Efecto de brillo en la barra */}
          <Animated.View
            style={[
              styles.progressShine,
              {
                opacity: progress.interpolate({
                  inputRange: [0, 0.1, 0.9, 1],
                  outputRange: [0, 0.8, 0.8, 0],
                  extrapolate: 'clamp',
                })
              }
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          {Math.round(loadingProgress)}%
        </Text>
      </Animated.View>

      {/* Texto de carga dinámico */}
      <Animated.View style={[styles.loadingTextContainer, { opacity: progressOpacity }]}>
        <Text style={styles.loadingText}>{loadingText}</Text>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  logoContainer: {
    marginBottom: 30,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 60,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#e2e8f0',
    borderRadius: 3,
    marginBottom: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#2563eb',
    borderRadius: 3,
    position: 'relative',
  },
  progressShine: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '600',
  },
  loadingTextContainer: {
    position: 'absolute',
    bottom: 80,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
    fontWeight: '500',
  },
});

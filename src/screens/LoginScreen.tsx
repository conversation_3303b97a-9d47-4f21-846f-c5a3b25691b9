import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import Logo from '../components/Logo';
import SimpleInput from '../components/SimpleInput';

type Props = NativeStackScreenProps<RootStackParamList, 'Login'>;

export default function LoginScreen({ navigation }: Props) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');

  const validateEmail = (text: string) => {
    setEmail(text);
    if (emailError) setEmailError('');
  };

  const validatePassword = (text: string) => {
    setPassword(text);
    if (passwordError) setPasswordError('');
  };

  const handleLogin = () => {
    let hasError = false;

    // Validar email
    if (!email.trim()) {
      setEmailError('Este campo es requerido');
      hasError = true;
    } else if (email.length < 3) {
      setEmailError('Debe tener al menos 3 caracteres');
      hasError = true;
    }

    // Validar contraseña
    if (!password.trim()) {
      setPasswordError('Este campo es requerido');
      hasError = true;
    } else if (password.length < 4) {
      setPasswordError('Debe tener al menos 4 caracteres');
      hasError = true;
    }

    if (!hasError) {
      navigation.replace('MyCorrespondences');
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <Logo size={100} />
      <Text style={styles.appName}>CorrespondexApp</Text>
      <Text style={styles.subtitle}>Manage your correspondence efficiently</Text>

      <View style={styles.formContainer}>
        <SimpleInput
          label="Email / Usuario / Teléfono"
          value={email}
          onChangeText={validateEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          error={emailError}
        />

        <SimpleInput
          label="Contraseña"
          value={password}
          onChangeText={validatePassword}
          secureTextEntry
          error={passwordError}
        />

        <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
          <Text style={styles.loginButtonText}>Entrar al Sistema</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.forgotButton}>
          <Text style={styles.forgotText}>¿Olvidaste tu contraseña?</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8fafc'
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    color: '#1e293b'
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    marginBottom: 32,
    textAlign: 'center'
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
  },
  loginButton: {
    backgroundColor: '#2563eb',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginTop: 8,
    marginBottom: 16,
    shadowColor: '#2563eb',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  forgotButton: {
    paddingVertical: 8,
    alignItems: 'center',
  },
  forgotText: {
    color: '#2563eb',
    fontSize: 14,
    fontWeight: '500',
  },
});
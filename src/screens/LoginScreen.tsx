import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Switch } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import Logo from '../components/Logo';
import FloatingInput from '../components/FloatingInput';

type Props = NativeStackScreenProps<RootStackParamList, 'Login'>;

export default function LoginScreen({ navigation }: Props) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [rememberCredentials, setRememberCredentials] = useState(false);

  const validateEmail = (text: string) => {
    setEmail(text);
    if (emailError) setEmailError('');
  };

  const validatePassword = (text: string) => {
    setPassword(text);
    if (passwordError) setPasswordError('');
  };

  const handleLogin = () => {
    let hasError = false;

    // Validar email
    if (!email.trim()) {
      setEmailError('Este campo es requerido');
      hasError = true;
    } else if (email.length < 3) {
      setEmailError('Debe tener al menos 3 caracteres');
      hasError = true;
    }

    // Validar contraseña
    if (!password.trim()) {
      setPasswordError('Este campo es requerido');
      hasError = true;
    } else if (password.length < 4) {
      setPasswordError('Debe tener al menos 4 caracteres');
      hasError = true;
    }

    if (!hasError) {
      // Aquí puedes agregar lógica para guardar credenciales si rememberCredentials es true
      console.log('Recordar credenciales:', rememberCredentials);
      navigation.replace('MyCorrespondences');
    }
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Logo size={100} />
      <Text style={styles.appName}>CorrespondexApp</Text>
      <Text style={styles.subtitle}>Manage your correspondence efficiently</Text>

      <View style={styles.formContainer}>
        <FloatingInput
          label="Email / Usuario / Teléfono"
          value={email}
          onChangeText={validateEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          error={emailError}
        />

        <FloatingInput
          label="Contraseña"
          value={password}
          onChangeText={validatePassword}
          secureTextEntry
          error={passwordError}
        />

        <View style={styles.rememberContainer}>
          <Text style={styles.rememberText}>Recordar Credenciales</Text>
          <Switch
            value={rememberCredentials}
            onValueChange={setRememberCredentials}
            trackColor={{ false: '#e2e8f0', true: '#93c5fd' }}
            thumbColor={rememberCredentials ? '#2563eb' : '#64748b'}
            ios_backgroundColor="#e2e8f0"
          />
        </View>

        <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
          <Text style={styles.loginButtonText}>Entrar al Sistema</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.forgotButton}>
          <Text style={styles.forgotText}>¿Olvidaste tu contraseña?</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8fafc',
    minHeight: '100%'
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
    color: '#1e293b'
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    marginBottom: 32,
    textAlign: 'center'
  },
  formContainer: {
    width: '100%',
    maxWidth: 400,
  },
  loginButton: {
    backgroundColor: '#2563eb',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginTop: 8,
    marginBottom: 16,
    shadowColor: '#2563eb',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  forgotButton: {
    paddingVertical: 8,
    alignItems: 'center',
  },
  forgotText: {
    color: '#2563eb',
    fontSize: 14,
    fontWeight: '500',
  },
  rememberContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 4,
    marginBottom: 8,
  },
  rememberText: {
    fontSize: 16,
    color: '#64748b',
    fontWeight: '500',
  },
});
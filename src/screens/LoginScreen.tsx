import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';
import Logo from '../components/Logo';

type Props = NativeStackScreenProps<RootStackParamList, 'Login'>;

export default function LoginScreen({ navigation }: Props) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  return (
    <View style={styles.container}>
      <Logo size={100} />
      <Text style={styles.appName}>CorrespondexApp</Text>
      <Text style={styles.subtitle}>Sistema de Gestión de Correspondencia.</Text>
      <TextInput style={styles.input} placeholder="Usuario / Correo Electrónico / Telefono" value={email} onChangeText={setEmail} keyboardType="email-address" />
      <TextInput style={styles.input} placeholder="Contraseña" value={password} onChangeText={setPassword} secureTextEntry />
      <Button title="Entrar al Sistema" onPress={() => navigation.replace('MyCorrespondences')} />
      <Text style={styles.forgot}>Olvidate tu acceso?</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex:1, justifyContent:'center', alignItems:'center', padding:16, backgroundColor:'#f8fafc' },
  appName: { fontSize:28, fontWeight:'bold', marginTop:16, marginBottom:8, color:'#1e293b' },
  subtitle: { fontSize:16, color:'#64748b', marginBottom:32, textAlign:'center' },
  input: { width:'100%', height:48, borderWidth:1, marginBottom:12, borderRadius:8, padding:12, backgroundColor:'white', borderColor:'#e2e8f0' },
  forgot: { marginTop:12, color:'#2563eb', fontSize:16 },
});
import React from 'react';
import { View, Text, StyleSheet, FlatList, Button, TextInput } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../App';

type Props = NativeStackScreenProps<RootStackParamList, 'MyCorrespondences'>;

const data = [
  { id:'1', name:'<PERSON>', address:'47 Abecister street', phone:'(600) 245‑6709' },
  { id:'2', name:'<PERSON>', address:'43 Abecister street', phone:'(600) 245‑6030' },
  { id:'3', name:'<PERSON>', address:'(452) 555‑6000' },
];

export default function MyCorrespondencesScreen({ navigation }: Props) {
  const renderItem = ({ item }) => (
    <View style={styles.item}>
      <View style={styles.checkboxPlaceholder}/>
      <View>
        <Text style={styles.name}>{item.name}</Text>
        <Text style={styles.sub}>{item.address}</Text>
        <Text style={styles.sub}>{item.phone}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.searchRow}>
        <Button title="Filter" onPress={()=>{}} />
        <Button title="Assigned" onPress={()=>{}} />
      </View>
      <TextInput style={styles.search} placeholder="Search" />
      <FlatList
        data={data}
        keyExtractor={i=>i.id}
        renderItem={renderItem}
      />
      <View style={styles.buttonsRow}>
        <Button title="Plan" onPress={() => navigation.navigate('Planner')} />
        <Button title="Process" onPress={()=>{}} />
        <Button title="Deliver" onPress={()=>{}} />
        <Button title="Return" onPress={()=>{}} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex:1, padding:12 },
  searchRow: { flexDirection:'row', justifyContent:'space-around', marginBottom:8 },
  search: { height:40, borderWidth:1, marginBottom:8, borderRadius:4, padding:8 },
  item: { flexDirection:'row', padding:8, borderBottomWidth:1 },
  checkboxPlaceholder: { width:24, height:24, borderWidth:1, marginRight:8 },
  name: { fontWeight:'bold' },
  sub: { color:'gray' },
  buttonsRow: { flexDirection:'row', justifyContent:'space-around', marginVertical:12 },
});
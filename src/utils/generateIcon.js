const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// SVG del logo optimizado para icono
const logoSVG = `<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="#2563eb" stroke="#1d4ed8" stroke-width="8"/>

  <!-- Envelope base -->
  <rect x="106" y="170" width="300" height="214" rx="16" fill="#ffffff" stroke="#e5e7eb" stroke-width="4"/>

  <!-- Envelope flap -->
  <path d="M106 170 L256 278 L406 170 Z" fill="#f3f4f6" stroke="#d1d5db" stroke-width="4"/>

  <!-- Letter lines inside envelope -->
  <line x1="150" y1="235" x2="362" y2="235" stroke="#9ca3af" stroke-width="6"/>
  <line x1="150" y1="265" x2="320" y2="265" stroke="#9ca3af" stroke-width="6"/>
  <line x1="150" y1="295" x2="342" y2="295" stroke="#9ca3af" stroke-width="6"/>

  <!-- Address/location pin icon -->
  <circle cx="320" cy="106" r="34" fill="#ef4444"/>
  <circle cx="320" cy="106" r="13" fill="#ffffff"/>

  <!-- Delivery truck icon -->
  <rect x="128" y="406" width="68" height="34" rx="8" fill="#059669"/>
  <circle cx="145" cy="453" r="13" fill="#374151"/>
  <circle cx="179" cy="453" r="13" fill="#374151"/>

  <!-- Connection lines -->
  <line x1="256" y1="128" x2="256" y2="170" stroke="#6b7280" stroke-width="4" stroke-dasharray="8,8"/>
  <line x1="192" y1="149" x2="234" y2="170" stroke="#6b7280" stroke-width="4" stroke-dasharray="8,8"/>
  <line x1="320" y1="149" x2="278" y2="170" stroke="#6b7280" stroke-width="4" stroke-dasharray="8,8"/>
</svg>`;

// Tamaños para Android
const androidSizes = [
  { size: 48, folder: 'mipmap-mdpi' },
  { size: 72, folder: 'mipmap-hdpi' },
  { size: 96, folder: 'mipmap-xhdpi' },
  { size: 144, folder: 'mipmap-xxhdpi' },
  { size: 192, folder: 'mipmap-xxxhdpi' }
];

// Tamaños para iOS
const iosSizes = [
  { size: 20, name: '20x20' },
  { size: 29, name: '29x29' },
  { size: 40, name: '40x40' },
  { size: 60, name: '60x60' },
  { size: 76, name: '76x76' },
  { size: 83.5, name: '83.5x83.5' },
  { size: 1024, name: '1024x1024' }
];

async function generateIcons() {
  console.log('Generando iconos...');

  // Crear directorio temporal
  const tempDir = path.join(__dirname, '../../temp_icons');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  // Generar iconos para Android
  for (const { size, folder } of androidSizes) {
    try {
      const outputPath = path.join(tempDir, `android_${folder}_${size}x${size}.png`);
      await sharp(Buffer.from(logoSVG))
        .resize(size, size)
        .png()
        .toFile(outputPath);
      console.log(`✅ Generado: android_${folder}_${size}x${size}.png`);
    } catch (error) {
      console.error(`❌ Error generando ${folder}:`, error.message);
    }
  }

  // Generar iconos para iOS
  for (const { size, name } of iosSizes) {
    try {
      const actualSize = Math.round(size);
      const outputPath = path.join(tempDir, `ios_${name.replace('.', '_')}.png`);
      await sharp(Buffer.from(logoSVG))
        .resize(actualSize, actualSize)
        .png()
        .toFile(outputPath);
      console.log(`✅ Generado: ios_${name}.png`);
    } catch (error) {
      console.error(`❌ Error generando ${name}:`, error.message);
    }
  }

  console.log(`\n🎉 Iconos generados en: ${tempDir}`);
  console.log('\n📋 Próximos pasos:');
  console.log('1. Copia los iconos de Android a las carpetas correspondientes en android/app/src/main/res/');
  console.log('2. Copia los iconos de iOS a ios/CorrespondexApp/Images.xcassets/AppIcon.appiconset/');
  console.log('3. Actualiza el archivo Contents.json en iOS');
}

generateIcons().catch(console.error);

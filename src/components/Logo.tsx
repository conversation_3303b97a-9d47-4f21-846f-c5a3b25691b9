import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Circle, Rect, Path, Line } from 'react-native-svg';

interface LogoProps {
  size?: number;
}

export default function Logo({ size = 120 }: LogoProps) {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size} viewBox="0 0 120 120">
        {/* Background circle */}
        <Circle cx="60" cy="60" r="55" fill="#2563eb" stroke="#1d4ed8" strokeWidth="2"/>
        
        {/* Envelope base */}
        <Rect x="25" y="40" width="70" height="50" rx="4" fill="#ffffff" stroke="#e5e7eb" strokeWidth="1"/>
        
        {/* Envelope flap */}
        <Path d="M25 40 L60 65 L95 40 Z" fill="#f3f4f6" stroke="#d1d5db" strokeWidth="1"/>
        
        {/* Letter lines inside envelope */}
        <Line x1="35" y1="55" x2="85" y2="55" stroke="#9ca3af" strokeWidth="1.5"/>
        <Line x1="35" y1="62" x2="75" y2="62" stroke="#9ca3af" strokeWidth="1.5"/>
        <Line x1="35" y1="69" x2="80" y2="69" stroke="#9ca3af" strokeWidth="1.5"/>
        
        {/* Address/location pin icon */}
        <Circle cx="75" cy="25" r="8" fill="#ef4444"/>
        <Circle cx="75" cy="25" r="3" fill="#ffffff"/>
        
        {/* Delivery truck icon (small) */}
        <Rect x="30" y="95" width="16" height="8" rx="2" fill="#059669"/>
        <Circle cx="34" cy="106" r="3" fill="#374151"/>
        <Circle cx="42" cy="106" r="3" fill="#374151"/>
        
        {/* Connection lines (representing network/correspondence) */}
        <Line x1="60" y1="30" x2="60" y2="40" stroke="#6b7280" strokeWidth="1" strokeDasharray="2,2"/>
        <Line x1="45" y1="35" x2="55" y2="40" stroke="#6b7280" strokeWidth="1" strokeDasharray="2,2"/>
        <Line x1="75" y1="35" x2="65" y2="40" stroke="#6b7280" strokeWidth="1" strokeDasharray="2,2"/>
      </Svg>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
